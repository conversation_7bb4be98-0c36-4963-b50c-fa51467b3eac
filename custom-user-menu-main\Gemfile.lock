GEM
  remote: https://rubygems.org/
  specs:
    activesupport (8.0.2)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
      uri (>= 0.13.1)
    ast (2.4.2)
    base64 (0.2.0)
    benchmark (0.4.0)
    bigdecimal (3.1.9)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.0)
    drb (2.2.1)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    json (2.10.2)
    language_server-protocol (3.17.0.4)
    lint_roller (1.1.0)
    logger (1.6.6)
    minitest (5.25.5)
    parallel (1.26.3)
    parser (3.3.7.1)
      ast (~> 2.4.1)
      racc
    prettier_print (1.2.1)
    racc (1.8.1)
    rack (3.1.12)
    rainbow (3.1.1)
    regexp_parser (2.10.0)
    rubocop (1.74.0)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.38.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.39.0)
      parser (>= 3.3.1.0)
    rubocop-capybara (2.22.1)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
    rubocop-discourse (3.12.1)
      activesupport (>= 6.1)
      lint_roller (>= 1.1.0)
      rubocop (>= 1.73.2)
      rubocop-capybara (>= 2.22.0)
      rubocop-factory_bot (>= 2.27.0)
      rubocop-rails (>= 2.30.3)
      rubocop-rspec (>= 3.0.1)
      rubocop-rspec_rails (>= 2.31.0)
    rubocop-factory_bot (2.27.1)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
    rubocop-rails (2.30.3)
      activesupport (>= 4.2.0)
      lint_roller (~> 1.1)
      rack (>= 1.1)
      rubocop (>= 1.72.1, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    rubocop-rspec (3.5.0)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
    rubocop-rspec_rails (2.31.0)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
      rubocop-rspec (~> 3.5)
    ruby-progressbar (1.13.0)
    securerandom (0.4.1)
    syntax_tree (6.2.0)
      prettier_print (>= 1.2.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    uri (1.0.3)

PLATFORMS
  ruby

DEPENDENCIES
  rubocop-discourse
  syntax_tree

BUNDLED WITH
   2.6.6
