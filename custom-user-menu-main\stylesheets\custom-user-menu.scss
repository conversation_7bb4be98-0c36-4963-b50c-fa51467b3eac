.custom-user-menu-content {
  .fk-d-menu {
    &__inner-content {
      border-radius: var(--d-border-radius-large);
      min-width: 17rem;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      ul {
        list-style: none;
        margin: 0;
      }
      li {
        display: flex;
        align-items: center;
      }
      a,
      button {
        display: inline-flex;
        justify-content: flex-start;
        align-items: center;
        box-sizing: border-box;
        width: 100%;
        height: var(--d-sidebar-row-height);
        margin: 0 1rem;
        padding: 0 var(--d-sidebar-row-horizontal-padding);
        color: var(--d-sidebar-link-color);
        transition: background-color 0.25s;
        border-radius: var(--d-nav-pill-border-radius);
        transition: background-color 0.25s;
        .d-icon {
          font-size: var(--d-sidebar-section-link-icon-size);
          color: var(--d-sidebar-link-icon-color);
          margin-right: var(--d-sidebar-section-link-prefix-margin-right);
        }
        &:hover {
          background: var(--d-sidebar-highlight-background);
          color: var(--d-sidebar-highlight-color);
          outline: none;
        }
      }
    }

    &__user-info {
      padding: 1rem;
      display: grid;
      grid-template-areas:
        "avatar name"
        "avatar username";
      grid-template-columns: 3rem 1fr;
      grid-template-rows: auto 1fr;
      column-gap: 1rem;
      .avatar {
        grid-area: avatar;
      }
      .name {
        grid-area: name;
        font-weight: 700;
        color: var(--d-sidebar-active-color);
      }
      .username {
        grid-area: username;
        font-size: var(--font-down-1);
        color: var(--d-sidebar-active-color);
        &::before {
          content: "@";
        }
      }
    }

    &__profile-tab {
      padding: 1rem 0;
      margin: 0;
      border-top: 1px solid var(--primary-low);
      li.enabled .d-icon {
        color: var(--tertiary);
      }
      li.preferences {
        padding-bottom: 1rem;
      }
      li.logout {
        padding-top: 1rem;
        border-top: 1px solid var(--primary-low);
      }
    }
  }
}
