import Component from "@glimmer/component";
import { action } from "@ember/object";
import { on } from "@ember/modifier";
import { service } from "@ember/service";
import UserMenuProfileTabContent from "discourse/components/user-menu/profile-tab-content";
import avatar from "discourse/helpers/bound-avatar-template";
import DMenu from "float-kit/components/d-menu";
import UserStatusBubble from "discourse/components/header/user-dropdown/user-status-bubble";

export default class CustomUserMenuFNav extends Component {
  @service currentUser;

  @action
  onRegisterApi(api) {
    this.dMenu = api;
  }

  <template>
    {{#if this.currentUser}}
      <DMenu
        @placement="top-end"
        @modalForMobile={{true}}
        @identifier="custom-user-menu-f-nav"
        @triggerClass="icon btn-flat"
        @groupIdentifier="f-nav-custom-header"
        @onRegisterApi={{this.onRegisterApi}}
      >
        <:trigger>
          {{avatar this.currentUser.avatar_template "medium"}}
          {{#if this.currentUser.status}}
            <UserStatusBubble
              @timezone={{this.currentUser.user_option.timezone}}
              @status={{this.currentUser.status}}
            />
          {{/if}}
        </:trigger>

        <:content as |args|>
          <div class="fk-d-menu__user-info">
            {{avatar this.currentUser.avatar_template "medium"}}
            {{#if this.currentUser.name}}
              <span class="name">{{this.currentUser.name}}</span>
            {{/if}}
            <span class="username">{{this.currentUser.username}}</span>
          </div>

          <div {{on "click" args.close}} class="fk-d-menu__profile-tab">
            <UserMenuProfileTabContent @closeUserMenu={{args.close}} />
          </div>
        </:content>
      </DMenu>
    {{/if}}
  </template>
}
