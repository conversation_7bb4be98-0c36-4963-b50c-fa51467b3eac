// F NAV
.f-nav {
  display: flex;
  position: fixed;
  bottom: 0;
  width: 100%;
  height: var(--f-nav-height);
  border-top: 1px solid var(--primary-low);
  background-color: var(--header_background);
  margin: 0;
  padding-bottom: env(safe-area-inset-bottom);
  z-index: z("dropdown") - 1;
  transform: translateY(0);
  transition: transform 0.1s linear;

  .tab {
    color: var(--primary-high);
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    @include user-select(none);

    &:active {
      will-change: background;
      background: rgba(var(--primary-rgb), 0.06);
    }

    &.active {
      .d-icon,
      .btn .d-icon,
      .tab-label {
        color: var(--tertiary);
      }
    }

    .multi-tab-trigger {
      width: 100%;
      height: 100%;
      background: transparent;

      .d-icon {
        color: var(--primary-high);
      }

      .message-unread-indicator,
      .chat-channel-unread-indicator {
        display: flex;
        align-items: center;
        justify-content: center;
        @include badge-notification(8px, 18px, 14px, -20px, 1em);
        background-color: var(--tertiary-med-or-tertiary);
        &.-urgent {
          background-color: var(--success);
          .chat-channel-unread-indicator__number {
            display: none;
          }
        }
        .d-icon {
          z-index: 2;
        }
      }
      .message-unread-indicator {
        &:before {
          content: "";
          @include svg-icon(solid, envelope, var(--secondary), 1em, 1em);
          font-size: var(--font-down-3);
        }
      }
      .chat-channel-unread-indicator {
         &:before {
          content: "";
          @include svg-icon(solid, comment, var(--secondary), 1em, 1em);
          font-size: var(--font-down-3);
        }
      }
      div:nth-child(1) {
        z-index: 2;
      }
      div:nth-child(2) {
        margin-right: auto;
        z-index: 1;
      }
    }

    .message-icon,
    .chat-icon {
      width: 100%;
      height: 100%;
      background: transparent;
      .d-icon {
        color: var(--primary-high);
      }
      .message-unread-indicator,
      .chat-channel-unread-indicator {
        @include badge-notification(8px, 14px, 14px, -20px, 50%);
        background-color: var(--tertiary-med-or-tertiary);
        &.-urgent {
          @include badge-notification(8px, 14px, 14px, -20px, 1em);
          background-color: var(--success);
          font-size: var(--font-down-3);
          font-weight: 600;
        }
      }
    }

    .relative-date {
      position: absolute;
      top: 0.5em;
      margin-left: 1.25em;
      font-size: var(--font-down-2);
      color: var(--primary-medium);
    }

    .badge-notification {
      @include badge-notification(8px, 14px, 14px, -20px, 1em);
      &.has-incoming {
        background-color: var(--tertiary-med-or-tertiary);
        min-width: unset;
        width: 10px;
        height: 10px;
        border-radius: 50%;
      }
      &.unread-notifications {
        background-color: var(--tertiary-med-or-tertiary);
        font-size: var(--font-down-3);
        font-weight: 600;
        &:before,
        &:after {
          content: "";
          display: inline-flex;
          width: 1px;
        }
      }
      &.new-reviewables {
        background-color: var(--danger);
      }
      &.with-icon {
        min-width: 18px;
        line-height: 1.2;
        .d-icon {
          color: var(--secondary);
          font-size: var(--font-down-4);
        }
      }
    }

    .d-icon {
      width: 1.4em;
      height: 1.4em;
      @if $f-nav-show-labels == "true" {
        width: 1.2em;
        height: 1.2em;
        margin-top: -0.75em;
      }
    }

    .tab-label {
      position: absolute;
      transform: translateY(100%);
      font-size: var(--font-down-3);
      line-height: var(--line-height-medium);
    }
  }
}

// Multi Tab
.fk-d-menu {
  &.multi-tab-content {
    ul.messages-options {
      display: flex;
      flex-direction: row;
      gap: 0.75em;
      padding: 0.75em;
      margin: 0;
      list-style-type: none;
      li {
        a,
        button {
          .d-icon {
            width: 1.5em;
            height: 1.5em;
            color: var(--primary-high);
          }
        }
        &.messages-icon {
          .message-unread-indicator {
            @include badge-notification(14px, 14px, 14px, -20px, 1em);
            background-color: var(--success);
            font-size: var(--font-down-3);
            font-weight: 600;
            &:before,
            &:after {
              content: "";
              display: inline-flex;
              width: 1px;
            }
          }
        }

        &.chat-header-icon {
          .chat-channel-unread-indicator {
            @include badge-notification(14px, 14px, 14px, -20px, 50%);
            background-color: var(--tertiary-med-or-tertiary);
            &.-urgent {
              @include badge-notification(14px, 14px, 14px, -20px, 1em);
              background-color: var(--success);
              font-size: var(--font-down-3);
              font-weight: 600;
              &:before,
              &:after {
                content: "";
                display: inline-flex;
                width: 1px;
              }
              .chat-channel-unread-indicator__number {
                display: inline-flex;
              }
            }
          }
        }
      }
    }
  }
}

// F NAV hidden
body.f-nav-hidden {
  // Header hide everywhere but topic, pm and chat
  &:not(.archetype-regular):not(.archetype-private_message):not(.has-full-page-chat) {
    .d-header-wrap {
      animation: fadeOutZ 0.1s forwards linear;
    }
    .leaderboard {
      .ranking-col-names {
        top: 0;
      }
    }
    .offline-indicator {
      top: 0.5em;
    }
  }

  &:not(.has-full-page-chat) {
    .f-nav {
      transform: translateY(var(--f-nav-height));
      padding-bottom: 0;
      border-top: none;
    }
  }

  // Topic elements scroll down position
  .d-toc-toggle,
  .posts-filtered-notice,
  #topic-progress-wrapper:not(.docked) {
    bottom: env(safe-area-inset-bottom);
  }
}

// Topic elements scroll up position
html:not(.anon) {
  body:not(.f-nav-hidden) {
    .d-toc-toggle,
    .posts-filtered-notice,
    #topic-progress-wrapper:not(.docked) {
      bottom: calc(var(--f-nav-height) + env(safe-area-inset-bottom));
      transition: bottom 0.1s linear;
    }

    #reply-control.draft,
    #reply-control.saving {
      transform: translateY(calc(var(--f-nav-height) * -1));
      transition: transform 0.1s linear;
    }
  }

  &.ios-device {
    body:not(.f-nav-hidden) {
      #reply-control.draft,
      #reply-control.saving {
        transform: translateY(calc((var(--f-nav-height) + 100%) * -1));
        transition: transform 0.1s linear;
      }
    }
  }

  // Add padding to main outlet except /chat
  body:not(.has-full-page-chat) {
    #main-outlet {
      padding-bottom: calc(var(--f-nav-height) + env(safe-area-inset-bottom));
    }
  }
}

// Hide F NAV on /chat
.has-full-page-chat {
  .f-nav {
    display: none;
  }
}

// Custom User Menu in F-Nav
.fk-d-menu {
  &.custom-user-menu-f-nav-content {
    .d-modal__body {
      .fk-d-menu__user-info {
        display: flex;
        align-items: center;
        gap: 0.5em;
        padding: 1em;
        border-bottom: 1px solid var(--primary-low);

        .avatar {
          width: 2.5em;
          height: 2.5em;
        }

        .name {
          font-weight: bold;
          color: var(--primary);
        }

        .username {
          color: var(--primary-medium);
          font-size: var(--font-down-1);
        }
      }

      .fk-d-menu__profile-tab {
        padding: 0;

        .user-menu-profile-tab-content {
          .user-menu-items {
            margin: 0;
            padding: 0.5em;
          }
        }
      }
    }
  }
}

// Avatar with status bubble in f-nav
.f-nav {
  .tab {
    .user-status-bubble {
      position: absolute;
      bottom: -2px;
      right: -2px;
      width: 1em;
      height: 1em;
      border: 2px solid var(--header_background);
      border-radius: 50%;
    }
  }
}
