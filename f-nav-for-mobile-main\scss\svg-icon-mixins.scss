@import "svg-icons/fontawesome/solid";

$svg-prefix: 'data:image/svg+xml;utf-8,';

@mixin svg-icon($icon-set, $icon-name, $icon-color, $width, $height) {
  
  @if $icon-set == 'solid' {
    $svg-url: $svg-prefix + map-get($solid, $icon-name);
    -webkit-mask-image: url($svg-url);
  }
  
  width: $width;
  height: $height;
  background: $icon-color;
  -webkit-mask-size: $width $height;
  -webkit-mask-repeat: no-repeat;
}
