f_nav_tabs:
  description: "Set up your F-NAV tabs."
  type: objects
  default:
    [
      { "name": Home, "icon": house, "function": home },
      { "name": <PERSON><PERSON>, "icon": bars, "function": hamburger },
      { "name": Multi Tab, "icon": message, "function": multi },
      { "name": Notification, "icon": bell, "function": notificationMenu },
      { "name": Search, "icon": magnifying-glass, "function": search },
    ]
  schema:
    name: "tab"
    identifier: name
    properties:
      name:
        type: string
        required: true
      icon:
        type: string
        required: true
      destination:
        type: string
        validations:
          url: true
      function:
        type: enum
        choices:
          - none
          - home
          - hamburger
          - message
          - chat
          - multi
          - notificationToRoute
          - notificationMenu
          - customUserMenu
          - search
f_nav_show_labels:
  description: "Show tabs name under icon"
  type: bool
  default: false
profile_extra_items:
  description: "Add items to the profile menu which appears before the Log Out button."
  type: objects
  default:
    []
  schema:
    name: "item"
    identifier: label
    properties:
      icon:
        type: string
      label:
        type: string
        required: true
      url:
        type: string
        required: true
        validations:
          url: true
hide_hamburger:
  description: "Hide the default header hamburger icon. This should disable if you don't want to add hamburger tab to F NAV."
  type: bool
  default: true
hide_search:
  description: "Hide the default header search icon. This should disable if you don't want to add search tab to F NAV."
  type: bool
  default: true
hide_chat:
  description: "Hide the default header chat icon. This should disable if you don't want to add chat tab to F NAV."
  type: bool
  default: true
svg_icons:
  default: "angle-left|message"
  type: list
  list_type: "compact"
  description: "List of FontAwesome 6 icons used in this theme component"
